# Configuration file for linear probing experiments
# This file contains all parameters organized by functionality

# Model Configuration
model_config:
  # List of model names to evaluate
  models:
    - "ResNet50"
    - "ViT-L"
    - "CLIP"
    - "SigLIP"
    - "SigLIP2-ViT-B"
    - "SigLIP2-ViT-L"
    - "SigLIP2-ViT-SO400M"
  
  # GPU ID to use
  gpu: 7

# Dataset Configuration
dataset_config:
  # List of dataset names to evaluate
  datasets:
    - "<PERSON><PERSON>"
    - "HiCervix"
    - "JinWooChoi"
    - "FNAC2019"
    - "LDCC"
    - "Sipakmed"
    - "Barcelona"
    - "BCI"
    - "BCFCI"
    - "BMCC"
  
  # Batch size for training and evaluation
  batch_size: 64
  
  # Cache all images in memory for faster training and evaluation
  cache_data: false
  
  # Number of worker threads for data loading and caching (null means half of CPU cores)
  num_workers: null
  
  # Disable progress bar during data loading (useful for non-interactive terminals)
  disable_progress_bar: false

# Training Configuration
training_config:
  # Number of training epochs
  epochs: 50
  
  # Learning rate for optimizer
  lr: 0.001

# Evaluation Configuration
evaluation_config:
  # Compute bootstrap confidence intervals
  compute_ci: true
  
  # Number of bootstrap samples for confidence intervals
  n_bootstraps: 1000
  
  # List of metrics to use for table generation
  metrics:
    - "accuracy"
    - "auc"
    - "macro_f1"
  
  # Force recomputation even if result files already exist
  force_recompute: false
  
  # Use k-fold cross-validation instead of official train/val/test split
  kfold: false
  
  # Number of folds for k-fold cross-validation
  k: 3

# Output Configuration
output_config:
  # Results directory (relative to project root)
  results_dir: "results"
