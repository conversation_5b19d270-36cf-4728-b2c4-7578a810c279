#!/bin/bash

# Training script for linear probing experiments
# This script loads configuration from YAML file and runs main.py

# Set script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Default configuration file path
DEFAULT_CONFIG="$PROJECT_ROOT/config/bash_config.yaml"

# Function to display usage
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -c, --config PATH    Path to YAML configuration file (default: $DEFAULT_CONFIG)"
    echo "  -h, --help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Use default config"
    echo "  $0 -c custom_config.yaml             # Use custom config"
    echo "  $0 --config /path/to/config.yaml     # Use config with full path"
    echo ""
}

# Parse command line arguments
CONFIG_FILE="$DEFAULT_CONFIG"

while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Check if configuration file exists
if [[ ! -f "$CONFIG_FILE" ]]; then
    echo "Error: Configuration file not found: $CONFIG_FILE"
    echo ""
    echo "Please ensure the configuration file exists or specify a different path using -c option."
    exit 1
fi

# Convert relative path to absolute path if needed
if [[ ! "$CONFIG_FILE" = /* ]]; then
    CONFIG_FILE="$PROJECT_ROOT/$CONFIG_FILE"
fi

echo "=========================================="
echo "Linear Probing Training Script"
echo "=========================================="
echo "Project root: $PROJECT_ROOT"
echo "Configuration file: $CONFIG_FILE"
echo "=========================================="
echo ""

# Change to project root directory
cd "$PROJECT_ROOT" || {
    echo "Error: Cannot change to project root directory: $PROJECT_ROOT"
    exit 1
}

# Check if main.py exists
if [[ ! -f "main.py" ]]; then
    echo "Error: main.py not found in project root: $PROJECT_ROOT"
    exit 1
fi

# Check if Python is available
if ! command -v python &> /dev/null; then
    echo "Error: Python is not installed or not in PATH"
    exit 1
fi

# Check if required Python packages are available
echo "Checking Python dependencies..."
python -c "import yaml, torch, numpy" 2>/dev/null || {
    echo "Error: Required Python packages are missing."
    echo "Please install the required packages:"
    echo "  pip install pyyaml torch numpy"
    exit 1
}

echo "All dependencies are available."
echo ""

# Run the training script with configuration file
echo "Starting training with configuration: $CONFIG_FILE"
echo "Command: python main.py --config \"$CONFIG_FILE\""
echo ""

# Execute the main script
python main.py --config "$CONFIG_FILE"

# Check exit status
EXIT_CODE=$?
echo ""
if [[ $EXIT_CODE -eq 0 ]]; then
    echo "=========================================="
    echo "Training completed successfully!"
    echo "=========================================="
else
    echo "=========================================="
    echo "Training failed with exit code: $EXIT_CODE"
    echo "=========================================="
fi

exit $EXIT_CODE
